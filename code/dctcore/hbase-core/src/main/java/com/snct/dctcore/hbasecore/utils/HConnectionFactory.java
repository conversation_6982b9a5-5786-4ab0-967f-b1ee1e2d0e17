package com.snct.dctcore.hbasecore.utils;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.io.IOException;


/**
 * @ClassName: HConnectionFactory
 * @Description: Hbase连接管理
 * @author: wzewei
 * @date: 2025-08-13 13:23
 */
@Component
public class HConnectionFactory implements InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(HConnectionFactory.class);

    @Value("${hbase.zookeeper.quorum}")
    private String zkQuorum;

    @Value("${hbase.master}")
    private String hBaseMaster;

    @Value("${hbase.zookeeper.property.clientPort}")
    private String zkPort;

    @Value("${zookeeper.znode.parent}")
    private String znode;

    private static Configuration conf = HBaseConfiguration.create();

    public static Connection connection;

    @Override
    public void afterPropertiesSet() throws Exception {
        conf.set("hbase.zookeeper.quorum", zkQuorum);
        conf.set("hbase.zookeeper.property.clientPort", zkPort);
        conf.set("zookeeper.znode.parent", znode);
        conf.set("hbase.master", hBaseMaster);

        try {
            connection = ConnectionFactory.createConnection(conf);
            logger.info("获取HBase connection连接成功！");
        } catch (IOException e) {
            logger.error("获取HBase connection连接失败！", e);
            throw e;
        }
    }
}

