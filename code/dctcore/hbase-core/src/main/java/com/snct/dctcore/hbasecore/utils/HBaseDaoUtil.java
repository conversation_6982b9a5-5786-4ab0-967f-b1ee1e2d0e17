package com.snct.dctcore.hbasecore.utils;

import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;
import com.snct.dctcore.commoncore.utils.DateUtils;
import com.snct.dctcore.commoncore.utils.RandomUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.hadoop.hbase.CompareOperator;
import org.apache.hadoop.hbase.NamespaceDescriptor;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.client.TableDescriptorBuilder;
import org.apache.hadoop.hbase.client.ColumnFamilyDescriptorBuilder;
import org.apache.hadoop.hbase.client.ColumnFamilyDescriptor;
import org.apache.hadoop.hbase.client.TableDescriptor;
import org.apache.hadoop.hbase.filter.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;

@Component("hBaseDaoUtil")
public class HBaseDaoUtil {

    protected final org.slf4j.Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 按天分区的预分区key
     */
    public static final byte[][] DAY_SPLIT_KEYS = new byte[][]{
            Bytes.toBytes("1"), Bytes.toBytes("2"), Bytes.toBytes("3"), Bytes.toBytes("4"),
            Bytes.toBytes("5"), Bytes.toBytes("6"), Bytes.toBytes("7"), Bytes.toBytes("8"), Bytes.toBytes("9"),
            Bytes.toBytes("10"), Bytes.toBytes("11"), Bytes.toBytes("12"), Bytes.toBytes("13"), Bytes.toBytes("14"),
            Bytes.toBytes("15"), Bytes.toBytes("16"), Bytes.toBytes("17"), Bytes.toBytes("18"), Bytes.toBytes("19"),
            Bytes.toBytes("20"), Bytes.toBytes("21"), Bytes.toBytes("22"), Bytes.toBytes("23"), Bytes.toBytes("24"),
            Bytes.toBytes("25"), Bytes.toBytes("26"), Bytes.toBytes("27"), Bytes.toBytes("28"), Bytes.toBytes("29"),
            Bytes.toBytes("30"), Bytes.toBytes("31")
    };

    // 关闭连接
    public static void close() {
        if (HConnectionFactory.connection != null) {
            try {
                HConnectionFactory.connection.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 创建命名空间
     */
    public void createNameSpace(String name) {
        try (Admin admin = HConnectionFactory.connection.getAdmin()) {
            NamespaceDescriptor nsd = NamespaceDescriptor.create(name).build();
            admin.createNamespace(nsd);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("创建命名空间失败！", e);
        }
    }

    /**
     * 删除命名空间
     */
    public void dropNameSpace(String name) {
        try (Admin admin = HConnectionFactory.connection.getAdmin()) {
            admin.deleteNamespace(name);
        } catch (IOException e) {
            logger.error("删除命名空间失败！", e);
        }
    }

    public boolean tableExists(String tableName) {
        boolean exists = false;
        TableName tn = TableName.valueOf(tableName);
        try (Admin admin = HConnectionFactory.connection.getAdmin()) {
            exists = admin.tableExists(tn);
        } catch (IOException e) {
            logger.error("查询失败！--{}", e);
        }
        return exists;
    }

    public void createTable(String tableName, Set<String> familyColumn) {
        createTable(tableName, familyColumn, null);
    }

    public void createTable(Set<String> tableNames, Set<String> familyColumn, byte[][] splitKeys) {
        for (String tableName : tableNames) {
            createTable(tableName, familyColumn, splitKeys);
        }
    }

    /**
     * 创建表
     */
    public void createTable(String tableName, Set<String> familyColumn, byte[][] splitKeys) {
        TableName tn = TableName.valueOf(tableName);
        try (Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (admin.tableExists(tn)) {
                logger.info("表已存在: {}", tableName);
                return;
            }

            TableDescriptorBuilder tableDescriptorBuilder = TableDescriptorBuilder.newBuilder(tn);

            if (familyColumn != null && !familyColumn.isEmpty()) {
                for (String fc : familyColumn) {
                    ColumnFamilyDescriptor cfd = ColumnFamilyDescriptorBuilder
                            .newBuilder(Bytes.toBytes(fc))
                            .build();
                    tableDescriptorBuilder.setColumnFamily(cfd);
                }
            } else {
                // 如果没有列簇，抛出或创建一个默认列簇
                ColumnFamilyDescriptor cfd = ColumnFamilyDescriptorBuilder
                        .newBuilder(Bytes.toBytes("cf"))
                        .build();
                tableDescriptorBuilder.setColumnFamily(cfd);
            }

            TableDescriptor td = tableDescriptorBuilder.build();

            if (splitKeys != null && splitKeys.length > 0) {
                admin.createTable(td, splitKeys);
            } else {
                admin.createTable(td);
            }
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("创建" + tableName + "表失败！", e);
        }
    }

    /**
     * 表重命名
     */
    public void tableRename(String oldName, String newName) {
        TableName oldTn = TableName.valueOf(oldName);
        TableName newTn = TableName.valueOf(newName);

        try (Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.tableExists(oldTn)) {
                logger.warn("要重命名的表不存在: {}", oldName);
                return;
            }
            admin.disableTable(oldTn);

            // 创建快照
            String snapshot = RandomUtil.randomStr(6);
            admin.snapshot(snapshot, oldTn);

            // 克隆到新表
            admin.cloneSnapshot(snapshot, newTn);

            // 删除快照
            admin.deleteSnapshot(snapshot);

            admin.deleteTable(oldTn);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("重命名" + oldName + "表失败！", e);
        }
    }

    /**
     * 删除表
     */
    public void dropTable(String tableName) {
        TableName tn = TableName.valueOf(tableName);
        try (Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.tableExists(tn)) {
                logger.warn("表不存在: {}", tableName);
                return;
            }
            if (!admin.isTableDisabled(tn)) {
                admin.disableTable(tn);
            }
            admin.deleteTable(tn);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("删除" + tableName + "表失败！", e);
        }
    }

    /**
     * 根据条件过滤查询
     */
    public <T> List<T> queryScan(T obj, Map<String, String> param) throws Exception {
        List<T> objs = new ArrayList<>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        TableName tn = TableName.valueOf(tableName);
        try (Table table = HConnectionFactory.connection.getTable(tn);
             Admin admin = HConnectionFactory.connection.getAdmin()) {

            if (!admin.tableExists(tn)) {
                return objs;
            }

            Scan scan = new Scan();
            if (param != null && !param.isEmpty()) {
                for (Map.Entry<String, String> entry : param.entrySet()) {
                    Class<?> clazz = obj.getClass();
                    Field[] fields = clazz.getDeclaredFields();
                    for (Field field : fields) {
                        if (!field.isAnnotationPresent(HBaseColumn.class)) {
                            continue;
                        }
                        field.setAccessible(true);
                        HBaseColumn orm = field.getAnnotation(HBaseColumn.class);
                        String family = orm.family();
                        String qualifier = orm.qualifier();
                        if (qualifier.equals(entry.getKey())) {
                            Filter filter = new SingleColumnValueFilter(Bytes.toBytes(family),
                                    Bytes.toBytes(entry.getKey()),
                                    CompareOperator.EQUAL,
                                    Bytes.toBytes(entry.getValue()));
                            scan.setFilter(filter);
                        }
                    }
                }
            }

            try (ResultScanner scanner = table.getScanner(scan)) {
                for (Result result : scanner) {
                    @SuppressWarnings("unchecked")
                    T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                    objs.add(beanClone);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询失败！", e);
            throw new Exception(e);
        }
        return objs;
    }

    /**
     * 根据rowkey查询
     */
    public <T> List<T> get(T obj, String... rowkeys) {
        List<T> objs = new ArrayList<>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return objs;
        }
        TableName tn = TableName.valueOf(tableName);
        try (Table table = HConnectionFactory.connection.getTable(tn);
             Admin admin = HConnectionFactory.connection.getAdmin()) {

            if (!admin.tableExists(tn)) {
                return objs;
            }

            List<Result> results = getResults(tableName, rowkeys);
            if (results.isEmpty()) {
                return objs;
            }
            for (Result result : results) {
                if (result == null || result.isEmpty()) {
                    continue;
                }
                try {
                    T bean = HBaseBeanUtil.resultToBean(result, obj);
                    objs.add(bean);
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("查询异常！", e);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("get 查询异常！", e);
        }
        return objs;
    }

    /**
     * 保存实体对象（会在需要时自动创建表）
     */
    public <T> boolean save(T... objs) {
        List<Put> puts = new ArrayList<>();
        String tableName = "";
        try (Admin admin = HConnectionFactory.connection.getAdmin()) {
            for (Object obj : objs) {
                if (obj == null) {
                    continue;
                }
                tableName = getORMTable(obj);
                TableName tn = TableName.valueOf(tableName);
                // 表不存在，先获取family创建表
                if (!admin.tableExists(tn)) {
                    Class<?> clazz = obj.getClass();
                    Field[] fields = clazz.getDeclaredFields();
                    Set<String> set = new HashSet<>(10);
                    for (Field field : fields) {
                        if (!field.isAnnotationPresent(HBaseColumn.class)) {
                            continue;
                        }
                        field.setAccessible(true);
                        HBaseColumn orm = field.getAnnotation(HBaseColumn.class);
                        String family = orm.family();
                        if ("rowkey".equalsIgnoreCase(family)) {
                            continue;
                        }
                        set.add(family);
                    }
                    createTable(tableName, set);
                }
                Put put = HBaseBeanUtil.beanToPut(obj);
                puts.add(put);
            }
        } catch (Exception e) {
            logger.error("保存Hbase异常！--{}", e);
        }
        return savePut(puts, tableName);
    }

    /**
     * 根据tableName保存
     */
    public <T> void save(String tableName, T... objs) {
        List<Put> puts = new ArrayList<>();
        for (Object obj : objs) {
            if (obj == null) {
                continue;
            }
            try {
                Put put = HBaseBeanUtil.beanToPut(obj);
                puts.add(put);
            } catch (Exception e) {
                logger.error("hbaseDao保存出错---{}", e);
            }
        }
        savePut(puts, tableName);
    }

    /**
     * 删除
     */
    public <T> void delete(T obj, String... rowkeys) {
        String tableName = getORMTable(obj);
        delete(tableName, rowkeys);
    }

    public <T> void delete(String tableName, String... rowkeys) {
        if (StringUtils.isBlank(tableName)) {
            return;
        }
        List<Delete> deletes = new ArrayList<>();
        for (String rowkey : rowkeys) {
            if (StringUtils.isBlank(rowkey)) {
                continue;
            }
            deletes.add(new Delete(Bytes.toBytes(rowkey)));
        }
        delete(deletes, tableName);
    }

    private void delete(List<Delete> deletes, String tableName) {
        if (StringUtils.isBlank(tableName)) {
            logger.info("tableName为空！");
            return;
        }
        TableName tn = TableName.valueOf(tableName);
        try (Table table = HConnectionFactory.connection.getTable(tn)) {
            table.delete(deletes);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("删除失败！", e);
        }
    }

    public void deleteByStartStop(String tableName, long startTime, long endTime) {
        if (StringUtils.isBlank(tableName)) {
            return;
        }
        List<Map<String, String>> rowMapList = getRowListByTime(startTime, endTime);

        ResultScanner scanner = null;
        TableName tn = TableName.valueOf(tableName);
        try (Table table = HConnectionFactory.connection.getTable(tn);
             Admin admin = HConnectionFactory.connection.getAdmin()) {

            if (!admin.tableExists(tn)) {
                return;
            }

            List<String> rowkeys = new ArrayList<>();
            for (Map<String, String> rowMap : rowMapList) {
                Scan scan = new Scan();
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scan.setFilter(new KeyOnlyFilter());
                scanner = table.getScanner(scan);
                for (Result result : scanner) {
                    rowkeys.add(Bytes.toString(result.getRow()));
                }
            }

            delete(tableName, rowkeys.toArray(new String[0]));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("deleteByStartStop:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("deleteByStartStop:关闭流异常！", e);
                }
            }
        }
    }

    /**
     * 根据 tableName 获取列簇名称
     */
    public List<String> familys(String tableName) {
        TableName tn = TableName.valueOf(tableName);
        try (Table table = HConnectionFactory.connection.getTable(tn)) {
            List<String> columns = new ArrayList<>();
            if (table == null) {
                return columns;
            }
            TableDescriptor tableDescriptor = table.getDescriptor();
            ColumnFamilyDescriptor[] columnDescriptors = tableDescriptor.getColumnFamilies();
            for (ColumnFamilyDescriptor columnDescriptor : columnDescriptors) {
                String columnName = columnDescriptor.getNameAsString();
                columns.add(columnName);
            }
            return columns;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询列簇名称失败！", e);
        }
        return Collections.emptyList();
    }

    private boolean savePut(List<Put> puts, String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return false;
        }
        TableName tn = TableName.valueOf(tableName);
        try (Table table = HConnectionFactory.connection.getTable(tn)) {
            if (puts != null && !puts.isEmpty()) {
                table.put(puts);
            }
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("savePut失败！", e);
            return false;
        }
    }

    private String getORMTable(Object obj) {
        HBaseTable table = obj.getClass().getAnnotation(HBaseTable.class);
        return table.tableName();
    }

    private List<Result> getResults(String tableName, String... rowkeys) {
        List<Result> resultList = new ArrayList<>();
        if (rowkeys == null || rowkeys.length == 0) {
            return resultList;
        }
        List<Get> gets = new ArrayList<>();
        for (String rowkey : rowkeys) {
            if (StringUtils.isBlank(rowkey)) {
                continue;
            }
            Get get = new Get(Bytes.toBytes(rowkey));
            gets.add(get);
        }
        TableName tn = TableName.valueOf(tableName);
        try (Table table = HConnectionFactory.connection.getTable(tn)) {
            Result[] results = table.get(gets);
            Collections.addAll(resultList, results);
            return resultList;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("getResults失败！", e);
            return resultList;
        }
    }

    /**
     * 根据条件（大于等于）过滤查询
     */
    public <T> List<T> queryScanGreater(T obj, Map<String, String> param) throws Exception {
        List<T> objs = new ArrayList<>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        TableName tn = TableName.valueOf(tableName);
        try (Table table = HConnectionFactory.connection.getTable(tn);
             Admin admin = HConnectionFactory.connection.getAdmin()) {

            if (!admin.tableExists(tn)) {
                return objs;
            }
            Scan scan = new Scan();
            if (param != null) {
                for (Map.Entry<String, String> entry : param.entrySet()) {
                    Class<?> clazz = obj.getClass();
                    Field[] fields = clazz.getDeclaredFields();
                    for (Field field : fields) {
                        if (!field.isAnnotationPresent(HBaseColumn.class)) {
                            continue;
                        }
                        field.setAccessible(true);
                        HBaseColumn orm = field.getAnnotation(HBaseColumn.class);
                        String family = orm.family();
                        String qualifier = orm.qualifier();
                        if (qualifier.equals(entry.getKey())) {
                            Filter filter = new SingleColumnValueFilter(Bytes.toBytes(family),
                                    Bytes.toBytes(entry.getKey()),
                                    CompareOperator.GREATER_OR_EQUAL,
                                    Bytes.toBytes(entry.getValue()));
                            scan.setFilter(filter);
                        }
                    }
                }
            }

            try (ResultScanner scanner = table.getScanner(scan)) {
                for (Result result : scanner) {
                    @SuppressWarnings("unchecked")
                    T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                    objs.add(beanClone);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询失败！", e);
            throw new Exception(e);
        }
        return objs;
    }

    public <T> List<T> queryScanRowkey(T obj, String rowkey) {
        List<T> objs = new ArrayList<>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        TableName tn = TableName.valueOf(tableName);
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(tn);
             Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.tableExists(tn)) {
                return objs;
            }

            Scan scan = new Scan();
            scan.setRowPrefixFilter(Bytes.toBytes(rowkey));
            scanner = table.getScanner(scan);
            for (Result result : scanner) {
                @SuppressWarnings("unchecked")
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return objs;
    }

    public <T> List<T> scanRowkeyList(T obj, List<String> rowkeyList) {
        List<T> objs = new ArrayList<>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        TableName tn = TableName.valueOf(tableName);
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(tn);
             Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.tableExists(tn)) {
                return objs;
            }

            Scan scan = new Scan();
            FilterList filterList = new FilterList(FilterList.Operator.MUST_PASS_ONE);
            for (String rowkey : rowkeyList) {
                RowFilter rowFilter = new RowFilter(CompareOperator.EQUAL, new BinaryComparator(Bytes.toBytes(rowkey)));
                filterList.addFilter(rowFilter);
            }
            scan.setFilter(filterList);
            scanner = table.getScanner(scan);
            for (Result result : scanner) {
                @SuppressWarnings("unchecked")
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("scanRowkeyList:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("scanRowkeyList:关闭流异常！", e);
                }
            }
        }
        return objs;
    }

    public <T> List<T> scanByStartStop(T obj, String tableName, String start, String stop) {
        List<T> objs = new ArrayList<>();
        if (StringUtils.isBlank(tableName)) {
            tableName = getORMTable(obj);
        }
        if (StringUtils.isBlank(tableName)) {
            return null;
        }

        TableName tn = TableName.valueOf(tableName);
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(tn);
             Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.tableExists(tn)) {
                return objs;
            }
            Scan scan = new Scan();
            scan.setStartRow(Bytes.toBytes(start));
            scan.setStopRow(Bytes.toBytes(stop));
            scanner = table.getScanner(scan);
            for (Result result : scanner) {
                @SuppressWarnings("unchecked")
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("scanByStartStop:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("scanByStartStop:关闭流异常！", e);
                }
            }
        }
        return objs;
    }

    public <T> List<T> scanByRowList(T obj, String tableName, List<Map<String, String>> rowList) {
        List<T> objs = new ArrayList<>();
        if (StringUtils.isBlank(tableName)) {
            return objs;
        }
        TableName tn = TableName.valueOf(tableName);
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(tn);
             Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.tableExists(tn)) {
                return objs;
            }
            List<Result> resultList = new ArrayList<>();
            for (Map<String, String> rowMap : rowList) {
                Scan scan = new Scan();
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scanner = table.getScanner(scan);
                for (Result result : scanner) {
                    resultList.add(result);
                }
            }
            objs = HBaseBeanUtil.resultToBean(resultList, obj, objs);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("scanByRowList:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("scanByRowList:关闭流异常！", e);
                }
            }
        }
        return objs;
    }

    public <T> Integer scanByStartStop4PageDesc(T obj, String tableName, List<T> objs, List<Map<String, String>> rowList, int currentPage, int pageSize, String sort) {
        List<byte[]> rows = new ArrayList<>();
        if (StringUtils.isBlank(tableName)) {
            return 0;
        }
        TableName tn = TableName.valueOf(tableName);
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(tn);
             Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.tableExists(tn)) {
                return 0;
            }

            int firstIndex = (currentPage > 1) ? (currentPage - 1) * pageSize : 0;
            int endIndex = currentPage * pageSize;

            for (Map<String, String> rowMap : rowList) {
                Scan scan = new Scan();
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scan.setFilter(new KeyOnlyFilter());
                scan.setCaching(5000);
                scanner = table.getScanner(scan);

                Result[] results;
                int size;
                do {
                    results = scanner.next(5000);
                    size = results.length;
                    for (Result r : results) {
                        rows.add(r.getRow());
                    }
                } while (size == 5000);
            }

            if ("descending".equals(sort)) {
                Collections.reverse(rows);
            }

            List<String> getRows = new ArrayList<>();
            for (int i = firstIndex; i < rows.size() && i <= endIndex; i++) {
                getRows.add(Bytes.toString(rows.get(i)));
            }

            List<Result> resultList = getResults(tableName, getRows.toArray(new String[0]));
            HBaseBeanUtil.resultToBean(resultList, obj, objs);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("scanByStartStop4PageDesc:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("scanByStartStop4PageDesc:关闭流异常！", e);
                }
            }
        }
        return rows.size();
    }

    public <T> T getLatestRow(T obj, String tableName) {
        return getLatestRow(obj, tableName, System.currentTimeMillis());
    }

    public <T> T getLatestRow(T obj, String tableName, Long lastTime) {
        return getLatestRow(obj, tableName, lastTime, 4);
    }

    public <T> T getLatestRow(T obj, String tableName, Long lastTime, Integer maxHead) {
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        TableName tn = TableName.valueOf(tableName);
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(tn);
             Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.tableExists(tn)) {
                return null;
            }

            long startTime = DateUtils.addDay(lastTime, -maxHead);
            List<Map<String, String>> rowKeyList = getRowListByTime(startTime, lastTime);

            for (Map<String, String> map : rowKeyList) {
                Scan scan = new Scan();
                scan.setStartRow(Bytes.toBytes(map.get("sr")));
                scan.setStopRow(Bytes.toBytes(map.get("er")));
                scanner = table.getScanner(scan);
                Result result = scanner.next();
                if (result != null) {
                    obj = HBaseBeanUtil.resultToBean(result, obj);
                    return obj;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("getLatestRow:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("getLatestRow:关闭流异常！", e);
                }
            }
        }
        return obj;
    }

    public <T> String getFirstRowKey(T obj, String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        TableName tn = TableName.valueOf(tableName);
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(tn);
             Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.tableExists(tn)) {
                return "";
            }

            long endTime = System.currentTimeMillis();
            long startTime = DateUtils.addDay(endTime, -(365 * 5));
            List<Map<String, String>> rowKeyList = getRowListByTime(startTime, endTime);
            Collections.reverse(rowKeyList);

            Result lastResult = null;
            int beforeLast = 0;
            for (Map<String, String> map : rowKeyList) {
                Scan scan = new Scan();
                scan.setStopRow(Bytes.toBytes(map.get("er")));
                scan.setStartRow(Bytes.toBytes(map.get("sr")));
                scanner = table.getScanner(scan);
                Result result = scanner.next();
                if (result != null) {
                    lastResult = result;
                    beforeLast = 0;
                    continue;
                }
                beforeLast++;
                if (beforeLast > 180) {
                    break;
                }
            }

            if (lastResult != null) {
                return Bytes.toString(lastResult.getRow());
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("getFirstRowKey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("getFirstRowKey:关闭流异常！", e);
                }
            }
        }
        return "";
    }

    public Integer queryNumByStartStop(String tableName, List<Map<String, String>> rowList) {
        Integer num = 0;
        if (StringUtils.isBlank(tableName)) {
            return num;
        }
        TableName tn = TableName.valueOf(tableName);
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(tn);
             Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.tableExists(tn)) {
                return 0;
            }
            for (Map<String, String> rowMap : rowList) {
                Scan scan = new Scan();
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scanner = table.getScanner(scan);
                while (scanner.next() != null) {
                    num++;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryNumByStartStop:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryNumByStartStop:关闭流异常！", e);
                }
            }
        }
        return num;
    }

    public List<Map<String, String>> getRowListByTime(Long startTime, Long endTime) {
        List<Map<String, Long>> timeList = DateUtils.splitDate(startTime, endTime);
        List<Map<String, String>> rowList = new ArrayList<>();
        for (Map<String, Long> tMap : timeList) {
            Map<String, String> rowMap = new HashMap<>();
            // 反转时间戳后, 较晚的时间对应较小的RowKey值
            rowMap.put("sr", getRowKey(tMap.get("et")));
            rowMap.put("er", getRowKey(tMap.get("st")));
            rowList.add(rowMap);
        }
        return rowList;
    }

    public String getRowKey(Long timeStamp) {
        int dayOfMonth = DateUtils.getDate(timeStamp);
        // 使用反转时间戳, 方便查询最新数据
        long reversedTimestamp = Long.MAX_VALUE - DateUtils.fetchWholeSecond(timeStamp);
        return dayOfMonth + "|" + reversedTimestamp;
    }

    public String getTableName(String sn, String typeStr, String deviceCode, Integer interval) {
        if (interval == 0) {
            return sn + ":" + typeStr + "_" + deviceCode;
        }
        return sn + ":" + typeStr + "_" + deviceCode + "-" + interval;
    }

    public String getTableName(String sn, String typeStr) {
        return sn + ":" + typeStr;
    }

    public String getTableName(String typeStr, String deviceCode, Integer interval) {
        if (interval == 100) {
            return typeStr + "_" + deviceCode + "-all";
        }
        if (interval == 0) {
            return typeStr + "_" + deviceCode;
        }
        return typeStr + "_" + deviceCode + "-" + interval;
    }

    ///**
    // * 全表扫描后在内存排序获取最新的一条
    // */
    //public <T> T getLatestRecord(T obj, String tableName) {
    //    if (StringUtils.isBlank(tableName)) {
    //        return null;
    //    }
    //    TableName tn = TableName.valueOf(tableName);
    //    ResultScanner scanner = null;
    //    try (Table table = HConnectionFactory.connection.getTable(tn)) {
    //        Scan scan = new Scan();
    //        scanner = table.getScanner(scan);
    //        List<Result> results = new ArrayList<>();
    //        Result result;
    //        while ((result = scanner.next()) != null) {
    //            results.add(result);
    //        }
    //        if (!results.isEmpty()) {
    //            results.sort((r1, r2) -> {
    //                String rowKey1 = Bytes.toString(r1.getRow());
    //                String rowKey2 = Bytes.toString(r2.getRow());
    //                return rowKey2.compareTo(rowKey1);
    //            });
    //            obj = HBaseBeanUtil.resultToBean(results.get(0), obj);
    //            return obj;
    //        }
    //    } catch (Exception e) {
    //        e.printStackTrace();
    //        logger.error("getLatestRecord:查询失败！", e);
    //    } finally {
    //        if (scanner != null) {
    //            try {
    //                scanner.close();
    //            } catch (Exception e) {
    //                e.printStackTrace();
    //                logger.error("getLatestRecord:关闭流异常！", e);
    //            }
    //        }
    //    }
    //    return null;
    //}



    // ============================改成倒叙时间戳后查询方法变更============================

    // 获取具体某一天的最新一条数据
    public <T> T getLatestRecordByDay(T obj, String tableName) {
        return getLatestRecordByDay(obj, tableName, System.currentTimeMillis());
    }

    public <T> T getLatestRecordByDay(T obj, String tableName, Long timeStamp) {
        TableName tn = TableName.valueOf(tableName);
        try (Table table = HConnectionFactory.connection.getTable(tn)) {
            Scan scan = new Scan();
            // 设置行前缀为指定天数
            scan.setFilter(new PrefixFilter(Bytes.toBytes(DateUtils.getDate(timeStamp) + "|")));
            scan.setLimit(1); // 只取第一条，就是最新的

            ResultScanner scanner = table.getScanner(scan);
            Result result = scanner.next();
            if (result != null) {
                return HBaseBeanUtil.resultToBean(result, obj);
            }
        } catch (Exception e) {
            logger.error("获取最新数据失败", e);
        }
        return null;
    }

    // 获取全表最新数据
    public <T> T getLatestRecord(T obj, String tableName) {
        TableName tn = TableName.valueOf(tableName);
        try (Table table = HConnectionFactory.connection.getTable(tn)) {

            T latestRecord = null;
            String latestRowKey = null;

            // 遍历所有预分区（1-31天）
            for (int day = 1; day <= 31; day++) {
                Scan scan = new Scan();
                scan.setFilter(new PrefixFilter(Bytes.toBytes(day + "|")));
                scan.setLimit(1); // 每个分区只取第一条

                ResultScanner scanner = table.getScanner(scan);
                Result result = scanner.next();

                if (result != null) {
                    String currentRowKey = Bytes.toString(result.getRow());
                    // 比较rowkey，反转时间戳下，较小的rowkey表示较新的时间
                    if (latestRowKey == null || currentRowKey.compareTo(latestRowKey) < 0) {
                        latestRowKey = currentRowKey;
                        latestRecord = HBaseBeanUtil.resultToBean(result, obj);
                    }
                }
                scanner.close();
            }

            return latestRecord;
        } catch (Exception e) {
            logger.error("获取最新数据失败", e);
        }
        return null;
    }


    // 指定时间范围内
    public <T> T getLatestRecord(T obj, String tableName, Long startTime, Long endTime) {
        List<Map<String, String>> rowList = getRowListByTime(startTime, endTime);

        TableName tn = TableName.valueOf(tableName);
        try (Table table = HConnectionFactory.connection.getTable(tn)) {

            // 由于是反转时间戳，第一个扫描到的就是最新的
            for (Map<String, String> rowMap : rowList) {
                Scan scan = new Scan();
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scan.setLimit(1); // 只取第一条

                ResultScanner scanner = table.getScanner(scan);
                Result result = scanner.next();

                if (result != null) {
                    return HBaseBeanUtil.resultToBean(result, obj);
                }
                scanner.close();
            }
        } catch (Exception e) {
            logger.error("获取最新数据失败", e);
        }
        return null;
    }

}
