# HBase 2.6.2 推荐配置示例
# 将这些配置添加到您的 application.yml 或 application.properties 中

# HBase 基础连接配置
hbase:
  # ZooKeeper 集群地址 (必填)
  zookeeper:
    quorum: 192.168.100.179  # 生产环境请使用实际的ZK集群地址，如: zk1,zk2,zk3
    property:
      clientPort: 2181  # ZooKeeper 客户端端口
  
  # HBase Master 地址 (可选，通常通过ZK自动发现)
  master: ""  # 如: hmaster1:16000
  
  # ZooKeeper 根节点路径
zookeeper:
  znode:
    parent: /hbase  # HBase 在 ZooKeeper 中的根路径

# HBase 客户端性能优化配置
hbase:
  client:
    # 连接池配置
    max:
      total:
        tasks: 100  # 客户端最大并发任务数
      perserver:
        tasks: 5    # 每个服务器最大并发任务数
      perregion:
        tasks: 1    # 每个Region最大并发任务数
    
    # 超时配置 (毫秒)
    operation:
      timeout: 120000  # 操作超时时间 (2分钟)
    scanner:
      timeout:
        period: 60000  # Scanner 超时时间 (1分钟)
  
  # RPC 配置
  rpc:
    timeout: 60000  # RPC 超时时间 (1分钟)

# 生产环境推荐配置
---
spring:
  profiles: production

hbase:
  zookeeper:
    quorum: zk1.example.com,zk2.example.com,zk3.example.com
    property:
      clientPort: 2181
  
  client:
    max:
      total:
        tasks: 200
      perserver:
        tasks: 10
      perregion:
        tasks: 2
    
    operation:
      timeout: 300000  # 5分钟
    scanner:
      timeout:
        period: 180000  # 3分钟
  
  rpc:
    timeout: 120000  # 2分钟

# 开发环境配置
---
spring:
  profiles: development

hbase:
  zookeeper:
    quorum: localhost
    property:
      clientPort: 2181
  
  client:
    max:
      total:
        tasks: 50
      perserver:
        tasks: 3
      perregion:
        tasks: 1
    
    operation:
      timeout: 60000
    scanner:
      timeout:
        period: 30000
  
  rpc:
    timeout: 30000
