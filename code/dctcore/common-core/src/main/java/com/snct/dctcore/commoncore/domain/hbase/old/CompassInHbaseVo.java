package com.snct.dctcore.commoncore.domain.hbase.old;

import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;

@HBaseTable
public class CompassInHbaseVo {
    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;
    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "i_t")
    private String initialTime;
    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    /**
     * 当前罗经数据-航向角
     */
    @HBaseColumn(family = "i", qualifier = "hehdt")
    private String hehdt;

    /**
     * 磁场X
     */
    @HBaseColumn(family = "i", qualifier = "cx")
    private String magneticXios;

    /**
     * 磁场X
     */
    @HBaseColumn(family = "i", qualifier = "cy")
    private String magneticYios;

    /**
     * 磁场X
     */
    @HBaseColumn(family = "i", qualifier = "cz")
    private String magneticZios;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getHehdt() {
        return hehdt;
    }

    public void setHehdt(String hehdt) {
        this.hehdt = hehdt;
    }

    public String getMagneticXios() {
        return magneticXios;
    }

    public void setMagneticXios(String magneticXios) {
        this.magneticXios = magneticXios;
    }

    public String getMagneticYios() {
        return magneticYios;
    }

    public void setMagneticYios(String magneticYios) {
        this.magneticYios = magneticYios;
    }

    public String getMagneticZios() {
        return magneticZios;
    }

    public void setMagneticZios(String magneticZios) {
        this.magneticZios = magneticZios;
    }
}
