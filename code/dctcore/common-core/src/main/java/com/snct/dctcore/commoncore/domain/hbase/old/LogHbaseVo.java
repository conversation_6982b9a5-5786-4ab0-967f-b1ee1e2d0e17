package com.snct.dctcore.commoncore.domain.hbase.old;


import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;

/**
 * @description:  Log数据 1秒一组
 * @author: rr
 * @create: 2020-06-03 10:34
 **/
@HBaseTable
public class LogHbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;
    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "i_t")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    /**
     * 1.船舶航行总里程
     */
    @Excel(name="船舶航行总里程")
    @HBaseColumn(family = "i", qualifier = "tsm")
    private String totalShipMileage;

    /**
     * 1.对水速度
     */
    @Excel(name="对水速度")
    @HBaseColumn(family = "i", qualifier = "ws")
    private String waterSpeed;

    /**
     * 3.mtw水温
     */
    @Excel(name="mtw水温")
    @HBaseColumn(family = "i", qualifier = "waterT")
    private String waterTemperature;

    /**
     * 1.节 船对水速度
     */
    @Excel(name="船对水速度(节)")
    @HBaseColumn(family = "i", qualifier = "wsn")
    private String waterSpeedN;

    /**
     * 2. 公里 船对水速度
     */
    @Excel(name="船对水速度(公里)")
    @HBaseColumn(family = "i", qualifier = "wsk")
    private String waterSpeedK;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getTotalShipMileage() {
        return totalShipMileage;
    }

    public void setTotalShipMileage(String totalShipMileage) {
        this.totalShipMileage = totalShipMileage;
    }

    public String getWaterSpeed() {
        return waterSpeed;
    }

    public void setWaterSpeed(String waterSpeed) {
        this.waterSpeed = waterSpeed;
    }

    public String getWaterTemperature() {
        return waterTemperature;
    }

    public void setWaterTemperature(String waterTemperature) {
        this.waterTemperature = waterTemperature;
    }

    public String getWaterSpeedN() {
        return waterSpeedN;
    }

    public void setWaterSpeedN(String waterSpeedN) {
        this.waterSpeedN = waterSpeedN;
    }

    public String getWaterSpeedK() {
        return waterSpeedK;
    }

    public void setWaterSpeedK(String waterSpeedK) {
        this.waterSpeedK = waterSpeedK;
    }
}
