package com.snct.dctcore.commoncore.domain.hbase;


import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;

/**
 * @ClassName: AttitudeHbaseVo
 * @Description: 姿态仪数据HBase存储实体类
 * @author: wzewei
 * @date: 2025-08-12 13:55
 */
@HBaseTable
public class AttitudeHbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "time")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "bj_time")
    private String initialBjTime;

    /**
     * 纬度
     */
    @HBaseColumn(family = "i", qualifier = "lat")
    private String latitude;

    /**
     * 经度
     */
    @HBaseColumn(family = "i", qualifier = "lon")
    private String longitude;

    /**
     * 横摇
     */
    @Excel(name="横摇")
    @HBaseColumn(family = "i", qualifier = "roll")
    private String roll;

    /**
     * 纵摇
     */
    @Excel(name="纵摇")
    @HBaseColumn(family = "i", qualifier = "pitch")
    private String pitch;

    /**
     * 升沉
     */
    @Excel(name="升沉")
    @HBaseColumn(family = "i", qualifier = "heave")
    private String heave;

    /**
     * 航向
     */
    @Excel(name="航向")
    @HBaseColumn(family = "i", qualifier = "heading")
    private String heading;

    /**
     * 速度
     */
    @Excel(name="速度")
    @HBaseColumn(family = "i", qualifier = "speed")
    private String speed;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoll() {
        return roll;
    }

    public void setRoll(String roll) {
        this.roll = roll;
    }

    public String getPitch() {
        return pitch;
    }

    public void setPitch(String pitch) {
        this.pitch = pitch;
    }

    public String getHeave() {
        return heave;
    }

    public void setHeave(String heave) {
        this.heave = heave;
    }

    public String getHeading() {
        return heading;
    }

    public void setHeading(String heading) {
        this.heading = heading;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}
